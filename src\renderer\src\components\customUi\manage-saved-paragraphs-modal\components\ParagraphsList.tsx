import React, { useMemo } from 'react';
import { <PERSON>rollArea } from "../../../ui/scroll-area";
import { But<PERSON> } from "../../../ui/button";
import { Badge } from "../../../ui/badge";
import {
  Edit3,
  Trash2,
  Download,
  Clock,
  FileText,
  Search,
  Plus
} from 'lucide-react';
import { SavedParagraphMetadata, SavedParagraphCategory } from '../../../../../types/global';
import { useSavedParagraphsStore } from '../../../../stores/savedParagraphsStore';

interface ParagraphsListProps {
  paragraphs: SavedParagraphMetadata[];
  categories: SavedParagraphCategory[];
  searchQuery: string;
  onEdit: (paragraphId: string) => void;
  onDelete: (paragraphId: string, title: string) => void;
  onInsert: (paragraphId: string) => void;
  onCreateNew: () => void;
  mode?: 'normal' | 'update' | 'insert'; // New prop to control the display mode
}

export const ParagraphsList: React.FC<ParagraphsListProps> = ({
  paragraphs,
  categories,
  searchQuery,
  onEdit,
  onDelete,
  onInsert,
  onCreateNew,
  mode = 'normal'
}) => {
  const { generateContentPreview } = useSavedParagraphsStore();

  // Get category by ID
  const getCategoryById = (categoryId: string) => {
    return categories.find(c => c.id === categoryId);
  };

  // Memoized paragraph list with previews
  const paragraphsWithPreviews = useMemo(() => {
    return paragraphs.map(paragraph => ({
      ...paragraph,
      preview: generateContentPreview(paragraph.contentPreview)
    }));
  }, [paragraphs, generateContentPreview]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Unknown';
    }
  };

  // Highlight search matches in text
  const highlightSearchMatch = (text: string, query: string) => {
    if (!query.trim()) return text;

    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return (
      <>
        {parts.map((part, index) =>
          regex.test(part) ? (
            <mark key={index} className="bg-accent/30 text-accent-foreground px-1 rounded">
              {part}
            </mark>
          ) : (
            part
          )
        )}
      </>
    );
  };

  if (paragraphsWithPreviews.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center py-12">
          {searchQuery.trim() ? (
            <>
              <Search className="h-12 w-12 text-foreground hover:text-foreground/80 transition-colors mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">No matches found</h3>
              <p className="text-muted-foreground">
                No paragraphs match your search for "{searchQuery}"
              </p>
            </>
          ) : (
            <>
              <FileText className="h-12 w-12 text-foreground hover:text-foreground/80 transition-colors mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                {mode === 'insert' ? 'No saved paragraphs to insert' : 'No saved paragraphs yet'}
              </h3>
              {mode !== 'insert' && (
                <Button
                  onClick={onCreateNew}
                  className="w-full mb-4"
                >
                  <Plus className="h-4 w-4 mr-2 text-foreground hover:text-foreground/80 transition-colors" />
                  Create Your First Paragraph
                </Button>
              )}
              <div className="text-muted-foreground max-w-md mx-auto space-y-3">
                <p className="text-center mb-4">
                  Save frequently used text snippets for quick access in future documents.
                </p>
                <div className="bg-muted/30 rounded-lg p-4 text-sm space-y-2">
                  <p className="font-medium text-foreground mb-2">Two ways to create saved paragraphs:</p>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <span className="bg-primary/10 text-primary rounded px-1.5 py-0.5 text-xs font-medium mt-0.5">1</span>
                      <span>Click the <strong>"New"</strong> button above to create a paragraph from scratch</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="bg-primary/10 text-primary rounded px-1.5 py-0.5 text-xs font-medium mt-0.5">2</span>
                      <span>Highlight text in your editor and use the <strong>floating toolbar button</strong> to save it instantly</span>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <ScrollArea className="h-full pr-4">
      <div className="space-y-3">
        {paragraphsWithPreviews.map((paragraph) => {
          const category = getCategoryById(paragraph.categoryId);

          return (
            <div
              key={paragraph.id}
              className="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors"
            >
              {/* Header with title and actions */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-foreground text-base leading-snug mb-1">
                    {highlightSearchMatch(paragraph.title, searchQuery)}
                  </h3>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-3 w-3 text-foreground hover:text-foreground/80 transition-colors" />
                    <span>{formatDate(paragraph.updatedAt)}</span>
                    {category && (
                      <>
                        <span>•</span>
                        <Badge
                          variant="secondary"
                          className="text-xs"
                          style={{
                            backgroundColor: category.color + '20',
                            borderColor: category.color + '40',
                            color: category.color
                          }}
                        >
                          {category.name}
                        </Badge>
                      </>
                    )}
                    <span>•</span>
                    <span>{paragraph.wordCount} words</span>
                  </div>
                </div>

                <div className="flex items-center gap-1 ml-3">
                  {mode === 'update' ? (
                    // Update mode: Show primary "Edit" button
                    <Button
                      onClick={() => onEdit(paragraph.id)}
                      className="h-8 px-3 text-sm"
                      title="Edit this paragraph"
                    >
                      Edit
                    </Button>
                  ) : mode === 'insert' ? (
                    // Insert mode: Show only "Insert" button with colored styling
                    <Button
                      onClick={() => onInsert(paragraph.id)}
                      className="h-8 px-3 text-sm bg-primary text-primary-foreground hover:bg-primary/90"
                      title="Insert into document"
                    >
                      Insert
                    </Button>
                  ) : (
                    // Normal mode: Show all action buttons
                    <>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onInsert(paragraph.id)}
                        className="h-8 w-8 bg-transparent hover:bg-transparent"
                        title="Insert into document"
                      >
                        <Download className="h-4 w-4 text-foreground hover:text-foreground/80 transition-colors" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onEdit(paragraph.id)}
                        className="h-8 w-8 bg-transparent hover:bg-transparent"
                        title="Edit paragraph"
                      >
                        <Edit3 className="h-4 w-4 text-foreground hover:text-foreground/80 transition-colors" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDelete(paragraph.id, paragraph.title)}
                        className="h-8 w-8 bg-transparent hover:bg-transparent"
                        title="Delete paragraph"
                      >
                        <Trash2 className="h-4 w-4 text-foreground hover:text-foreground/80 transition-colors" />
                      </Button>
                    </>
                  )}
                </div>
              </div>

              {/* Description */}
              {paragraph.description && (
                <p className="text-sm text-muted-foreground mb-3 italic">
                  {highlightSearchMatch(paragraph.description, searchQuery)}
                </p>
              )}

              {/* Content preview */}
              <div className="text-sm text-foreground bg-muted/30 rounded p-3 border border-border/50">
                <div className="line-clamp-3">
                  {highlightSearchMatch(paragraph.preview, searchQuery)}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );
};
