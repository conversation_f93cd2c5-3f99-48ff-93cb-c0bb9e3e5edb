import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { SettingsSidebarContextType } from './SettingsSidebarTypes';
import { useSidebarContext } from '../../../contexts/SidebarContext';
import { useSavedParagraphsStore } from '../../../stores/savedParagraphsStore';

const SettingsSidebarContext = createContext<SettingsSidebarContextType | undefined>(undefined);

export function SettingsSidebarProvider({ children }: { children: React.ReactNode }) {
  const { isLeftSidebarExpanded } = useSidebarContext();
  const [expanded, setExpanded] = useState(false);
  const [isPinned, setIsPinned] = useState(() => {
    if (typeof window !== 'undefined') {
      const storedPin = localStorage.getItem('settings-sidebar-pinned');
      return storedPin ? JSON.parse(storedPin) : false;
    }
    return false;
  });
  const [isUiExtensionOpen, setIsUiExtensionOpen] = useState(false);

  // Track API Keys modal state (local state from SettingsSidebarDocumentSettings)
  const [isApiKeysModalOpen, setIsApiKeysModalOpen] = useState(false);

  // Track saved paragraphs modal state automatically
  const { isModalOpen: isSavedParagraphsModalOpen } = useSavedParagraphsStore();

  // Automatically sync UI extension state with modal states
  useEffect(() => {
    // Update UI extension state based on any modal being open
    const hasModalOpen = isSavedParagraphsModalOpen || isApiKeysModalOpen;
    setIsUiExtensionOpen(hasModalOpen);
  }, [isSavedParagraphsModalOpen, isApiKeysModalOpen]);

  // Close this sidebar when left sidebar opens
  useEffect(() => {
    if (isLeftSidebarExpanded && expanded && !isPinned) {
      setExpanded(false);
    }
  }, [isLeftSidebarExpanded, expanded, isPinned]);

  useEffect(() => {
    if (isPinned) {
      setExpanded(true);
    }
  }, [isPinned]);

  const toggleExpanded = useCallback(() => {
    // If modal/extension is open and we're trying to close, don't allow it
    if (expanded && isPinned && isUiExtensionOpen) {
      return;
    }

    if (!isPinned) {
      setExpanded(prev => !prev);
    } else if (expanded && !isUiExtensionOpen) {
      setIsPinned(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('settings-sidebar-pinned', JSON.stringify(false));
      }
      setExpanded(false);
    } else if (!expanded && isPinned) {
       setExpanded(true);
    }
  }, [isPinned, expanded, isUiExtensionOpen]);

  const togglePinned = useCallback(() => {
    const newPinState = !isPinned;
    setIsPinned(newPinState);
    if (typeof window !== 'undefined') {
      localStorage.setItem('settings-sidebar-pinned', JSON.stringify(newPinState));
    }
    if (newPinState) {
      setExpanded(true);
    } else if (!newPinState && !isUiExtensionOpen) {
      setExpanded(false);
    }
  }, [isPinned, isUiExtensionOpen]);

  return (
    <SettingsSidebarContext.Provider value={{
      expanded,
      toggleExpanded,
      isPinned,
      togglePinned,
      isUiExtensionOpen,
      setIsUiExtensionOpen,
      isApiKeysModalOpen,
      setIsApiKeysModalOpen
    }}>
      {children}
    </SettingsSidebarContext.Provider>
  );
}

export function useSettingsSidebar() {
  const context = useContext(SettingsSidebarContext);
  if (context === undefined) {
    throw new Error('useSettingsSidebar must be used within a SettingsSidebarProvider');
  }
  return context;
}
